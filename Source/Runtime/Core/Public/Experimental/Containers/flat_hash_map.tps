<?xml version="1.0" encoding="utf-8"?>
<TpsData xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance">
  <Name>flat_hash_map</Name>
  <Location>//UE4/Main/Engine/Source/Runtime/Core/Public/Experimental/Containers</Location>
  <Function>This implements a high performance hash table, using Robin Hood Hashing. We use the algorithm and data representation from from flat_hash_map as a basis for our own hash table implementation. We do not use any part of flat_hash_map source code verbatim.</Function>
  <Eula>https://github.com/skarupke/flat_hash_map/raw/master/flat_hash_map.hpp</Eula>
  <RedistributeTo>
  </RedistributeTo>
  <LicenseFolder></LicenseFolder>
</TpsData>