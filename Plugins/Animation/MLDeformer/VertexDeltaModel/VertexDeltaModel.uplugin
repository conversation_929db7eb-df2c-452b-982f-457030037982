{"FileVersion": 3, "Version": 1, "VersionName": "0.3", "FriendlyName": "ML Deformer Vertex Delta Model", "Description": "Vertex Delta Model for the ML Deformer Framework", "Category": "Animation", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "https://docs.unrealengine.com/5.0/en-US/using-the-machine-learning-deformer-in-unreal-engine/", "MarketplaceURL": "", "SupportURL": "https://forums.unrealengine.com/", "CanContainContent": true, "IsBetaVersion": true, "IsExperimentalVersion": false, "Installed": false, "SupportedTargetPlatforms": ["Win64", "Linux", "<PERSON>"], "Modules": [{"Name": "VertexDeltaModel", "Type": "Runtime", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}, {"Name": "VertexDeltaModelEditor", "Type": "Editor", "LoadingPhase": "<PERSON><PERSON><PERSON>", "PlatformAllowList": ["Win64", "Linux", "<PERSON>"]}], "Plugins": [{"Name": "GeometryCache", "Enabled": true}, {"Name": "NNE", "Enabled": true}, {"Name": "NNERuntimeRDG", "Enabled": true}, {"Name": "DeformerGraph", "Enabled": true}, {"Name": "PythonFoundationPackages", "Enabled": true}, {"Name": "MLDeformerFramework", "Enabled": true}]}