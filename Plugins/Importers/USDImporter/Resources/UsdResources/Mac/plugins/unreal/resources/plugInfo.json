# Portions of this file auto-generated by usdGenSchema.
# Edits will survive regeneration except for comments and
# changes to types with autoGenerated=true.
{
    "Plugins": [
        {
            "Info": {
                "Types": {
                    "unrealControlRigAPI": {
                        "alias": {
                            "UsdSchemaBase": "ControlRigAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealGroomAPI": {
                        "alias": {
                            "UsdSchemaBase": "GroomAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Curves", 
                            "Xform"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealGroomBindingAPI": {
                        "alias": {
                            "UsdSchemaBase": "GroomBindingAPI"
                        }, 
                        "apiSchemaCanOnlyApplyTo": [
                            "Mesh", 
                            "SkelRoot"
                        ], 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }, 
                    "unrealLiveLinkAPI": {
                        "alias": {
                            "UsdSchemaBase": "LiveLinkAPI"
                        }, 
                        "autoGenerated": true, 
                        "bases": [
                            "UsdAPISchemaBase"
                        ], 
                        "schemaKind": "singleApplyAPI"
                    }
                }
            }, 
            "Name": "unreal", 
            "ResourcePath": "resources", 
            "Root": "..", 
            "Type": "resource"
        }
    ]
}
