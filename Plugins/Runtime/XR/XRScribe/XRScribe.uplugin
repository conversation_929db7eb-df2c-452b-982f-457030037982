{"FileVersion": 3, "Version": 1, "VersionName": "0.1", "FriendlyName": "XRScribe", "Description": "OpenXR API Capture/Emulation", "Category": "Virtual Reality", "CreatedBy": "Epic Games, Inc.", "CreatedByURL": "https://epicgames.com", "DocsURL": "", "MarketplaceURL": "", "SupportURL": "", "EnabledByDefault": false, "CanContainContent": false, "IsBetaVersion": false, "IsExperimentalVersion": true, "Installed": false, "SupportedTargetPlatforms": ["Win64"], "Modules": [{"Name": "XRScribe", "Type": "Runtime", "LoadingPhase": "PostConfigInit", "PlatformAllowList": ["Win64"]}], "Plugins": [{"Name": "OpenXR", "Enabled": true}]}